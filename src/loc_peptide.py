import argparse
from Bio import SeqIO
from Bio.Seq import Seq
import csv

# Parse command-line arguments
parser = argparse.ArgumentParser(description="Find peptide coordinates in FASTA reads and merge with annotation.")
parser.add_argument("peptide_file", help="File containing peptide sequences (one per line)")
parser.add_argument("fasta_file", help="FASTA file containing read sequences")
parser.add_argument("annotation_file", help="Annotation file with read-based data")
parser.add_argument("output_file", help="Output BED-like file")
args = parser.parse_args()

# Read peptide sequences
#with open(args.peptide_file, "r") as pf:
    #peptides = [line.strip(",")[0] for line in pf if line.strip("")]
    #print(peptides)

with open(args.peptide_file, "r") as pf:
    reader = csv.DictReader(pf)  # reads using header row
    peptides = [row["Peptide"] for row in reader]
    print(peptides)
# Read annotation data into a dictionary
annotations = {}
with open(args.annotation_file, "r") as af:
    for line in af:
        parts = line.strip().split()
        if len(parts) >= 7:  # Ensure minimum required columns exist
            read_id = parts[4]
            try:
                ann_start, ann_end = int(parts[5]), int(parts[6])
            except ValueError:
                continue  # Skip lines where start/end positions are not valid integers
            strand = parts[9] if len(parts) > 9 else "NA"
            family = parts[10] if len(parts) > 10 else "NA"
            class_ = parts[11] if len(parts) > 11 else "NA"
            
            if read_id not in annotations:
                annotations[read_id] = []
            annotations[read_id].append((ann_start, ann_end, strand, family, class_))

# Open output file
with open(args.output_file, "w", newline='') as out_f:
    writer = csv.writer(out_f, delimiter='\t')
    writer.writerow(["Peptide", "Read_ID", "Frame", "Start", "End", "ORF_Start", "ORF_End", 
                     "Ann_Start", "Ann_End", "Strand", "Family", "Class", "Peptide_Overlap", "ORF_Overlap"])
    
    # Iterate over FASTA records
    for record in SeqIO.parse(args.fasta_file, "fasta"):
        read_id = record.id
        sequence = str(record.seq)
        
        # Translate in three frames
        for frame in range(3):
            translated_seq = Seq(sequence[frame:]).translate(to_stop=False)
            translated_str = str(translated_seq)
            
            # Search for peptides in the translated sequence
            for peptide in peptides:
                start = 0  # Start searching from position 0
                while (start := translated_str.find(peptide, start)) != -1:
                    end = start + len(peptide)  # Compute end position
                    
                    # Convert peptide positions back to DNA coordinates
                    dna_start = frame + start * 3
                    dna_end = frame + end * 3
                    
                    # Determine ORF boundaries
                    orf_start_aa = translated_str.rfind('M', 0, start) if 'M' in translated_str[:start] else 0
                    orf_end_aa = translated_str.find('*', end) if '*' in translated_str[end:] else len(translated_str)
                    orf_start = frame + orf_start_aa * 3
                    orf_end = frame + orf_end_aa * 3
                    
                    # Default annotation values
                    ann_start, ann_end, strand, family, class_ = "NA", "NA", "NA", "NA", "NA"
                    peptide_overlap, orf_overlap = 0, 0
                    
                    # Check if read_id is in annotation
                    if read_id in annotations:
                        for ann_s, ann_e, ann_strand, ann_family, ann_class in annotations[read_id]:
                            if ann_s <= dna_start <= ann_e or ann_s <= dna_end <= ann_e:
                                peptide_overlap = 1
                            if ann_s <= orf_start <= ann_e or ann_s <= orf_end <= ann_e:
                                orf_overlap = 1
                            ann_start, ann_end, strand, family, class_ = ann_s, ann_e, ann_strand, ann_family, ann_class
                            
                    writer.writerow([peptide, read_id, frame, dna_start, dna_end, orf_start, orf_end, 
                                     ann_start, ann_end, strand, family, class_, peptide_overlap, orf_overlap])
                    start += 1  # Move to next possible position
