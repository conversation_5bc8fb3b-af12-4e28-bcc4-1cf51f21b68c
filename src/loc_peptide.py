import argparse
import csv

def parse_fasta(filename):
    """Parse FASTA file and return dictionary of sequences"""
    sequences = {}
    current_id = None
    current_seq = []

    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                if current_id is not None:
                    sequences[current_id] = ''.join(current_seq)
                current_id = line[1:]  # Remove '>' character
                current_seq = []
            else:
                current_seq.append(line)

        # Don't forget the last sequence
        if current_id is not None:
            sequences[current_id] = ''.join(current_seq)

    return sequences

def translate_dna(dna_seq, frame=0):
    """Translate DNA sequence to protein in specified frame"""
    codon_table = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
        'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }

    # Start from the specified frame
    dna_seq = dna_seq[frame:]
    protein = []

    for i in range(0, len(dna_seq) - 2, 3):
        codon = dna_seq[i:i+3].upper()
        if len(codon) == 3:
            protein.append(codon_table.get(codon, 'X'))

    return ''.join(protein)

# Parse command-line arguments
parser = argparse.ArgumentParser(description="Find peptide coordinates in FASTA reads and merge with annotation.")
parser.add_argument("peptide_file", help="File containing peptide sequences (one per line)")
parser.add_argument("fasta_file", help="FASTA file containing read sequences")
parser.add_argument("annotation_file", help="Annotation file with read-based data")
parser.add_argument("output_file", help="Output BED-like file")
args = parser.parse_args()

# Read peptide sequences
#with open(args.peptide_file, "r") as pf:
    #peptides = [line.strip(",")[0] for line in pf if line.strip("")]
    #print(peptides)

with open(args.peptide_file, "r") as pf:
    reader = csv.DictReader(pf)  # reads using header row
    peptides = [row["Peptide"] for row in reader]
    print(f"Loaded {len(peptides)} peptides")
    print(f"First few peptides: {peptides[:5]}")
# Read annotation data into a dictionary
annotations = {}
with open(args.annotation_file, "r") as af:
    line_count = 0
    for line in af:
        line_count += 1
        if line_count <= 3:  # Skip header lines
            continue
        parts = line.strip().split()
        if len(parts) >= 7:  # Ensure minimum required columns exist
            read_id = parts[4]
            try:
                ann_start, ann_end = int(parts[5]), int(parts[6])
            except ValueError:
                continue  # Skip lines where start/end positions are not valid integers
            strand = parts[9] if len(parts) > 9 else "NA"
            family = parts[10] if len(parts) > 10 else "NA"
            class_ = parts[11] if len(parts) > 11 else "NA"

            if read_id not in annotations:
                annotations[read_id] = []
            annotations[read_id].append((ann_start, ann_end, strand, family, class_))

print(f"Loaded annotations for {len(annotations)} reads")

# Open output file
with open(args.output_file, "w", newline='') as out_f:
    writer = csv.writer(out_f, delimiter='\t')
    writer.writerow(["Peptide", "Read_ID", "Frame", "Start", "End", "ORF_Start", "ORF_End", 
                     "Ann_Start", "Ann_End", "Strand", "Family", "Class", "Peptide_Overlap", "ORF_Overlap"])
    
    # Parse FASTA file
    sequences = parse_fasta(args.fasta_file)
    print(f"Loaded {len(sequences)} sequences from FASTA file")

    # Iterate over FASTA records
    for read_id, sequence in sequences.items():
        # Translate in three frames
        for frame in range(3):
            translated_str = translate_dna(sequence, frame)
            
            # Search for peptides in the translated sequence
            for peptide in peptides:
                start = 0  # Start searching from position 0
                while (start := translated_str.find(peptide, start)) != -1:
                    end = start + len(peptide)  # Compute end position
                    
                    # Convert peptide positions back to DNA coordinates
                    dna_start = frame + start * 3
                    dna_end = frame + end * 3
                    
                    # Determine ORF boundaries
                    orf_start_aa = translated_str.rfind('M', 0, start) if 'M' in translated_str[:start] else 0
                    orf_end_aa = translated_str.find('*', end) if '*' in translated_str[end:] else len(translated_str)
                    orf_start = frame + orf_start_aa * 3
                    orf_end = frame + orf_end_aa * 3
                    
                    # Check if read_id is in annotation and find overlapping annotations
                    if read_id in annotations:
                        overlapping_annotations = []

                        for ann_s, ann_e, ann_strand, ann_family, ann_class in annotations[read_id]:
                            # Check for peptide overlap (any overlap between peptide and annotation)
                            peptide_overlaps = not (dna_end < ann_s or dna_start > ann_e)

                            # Check for ORF overlap (any overlap between ORF and annotation)
                            orf_overlaps = not (orf_end < ann_s or orf_start > ann_e)

                            if peptide_overlaps or orf_overlaps:
                                overlapping_annotations.append({
                                    'ann_start': ann_s,
                                    'ann_end': ann_e,
                                    'strand': ann_strand,
                                    'family': ann_family,
                                    'class': ann_class,
                                    'peptide_overlap': 1 if peptide_overlaps else 0,
                                    'orf_overlap': 1 if orf_overlaps else 0
                                })

                        # Write a row for each overlapping annotation
                        if overlapping_annotations:
                            for ann in overlapping_annotations:
                                writer.writerow([peptide, read_id, frame, dna_start, dna_end, orf_start, orf_end,
                                               ann['ann_start'], ann['ann_end'], ann['strand'], ann['family'],
                                               ann['class'], ann['peptide_overlap'], ann['orf_overlap']])
                        else:
                            # No overlapping annotations found, but read has annotations
                            writer.writerow([peptide, read_id, frame, dna_start, dna_end, orf_start, orf_end,
                                           "NA", "NA", "NA", "NA", "NA", 0, 0])
                    else:
                        # No annotations for this read
                        writer.writerow([peptide, read_id, frame, dna_start, dna_end, orf_start, orf_end,
                                       "NA", "NA", "NA", "NA", "NA", 0, 0])
                    start += 1  # Move to next possible position
